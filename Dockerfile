FROM jenkins/jenkins:lts

# Switch to root to install Docker
USER root

# Install Docker CLI
RUN apt-get update && \
    apt-get install -y \
    apt-transport-https \
    ca-certificates \
    curl \
    gnupg \
    lsb-release && \
    curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg && \
    echo \
    "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian \
    $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null && \
    apt-get update && \
    apt-get install -y docker-ce-cli && \
    rm -rf /var/lib/apt/lists/*

# Add jenkins user to docker group
RUN groupadd -g 999 docker || true
RUN usermod -aG docker jenkins

# Install common build tools
RUN apt-get update && \
    apt-get install -y \
    git \
    build-essential \
    python3 \
    python3-pip \
    nodejs \
    npm && \
    rm -rf /var/lib/apt/lists/*

# Switch back to jenkins user
USER jenkins

# Install suggested plugins
RUN jenkins-plugin-cli --plugins \
    blueocean \
    docker-workflow \
    docker-plugin \
    pipeline-stage-view \
    git \
    workflow-aggregator
