version: '3.8'

services:
  jenkins:
    build: .
    container_name: jenkins-docker
    restart: unless-stopped
    ports:
      - "8080:8080"
      - "50000:50000"
    volumes:
      # Jenkins data persistence
      - jenkins_home:/var/jenkins_home
      # Mount Docker socket to enable Docker commands
      - /var/run/docker.sock:/var/run/docker.sock
      # Optional: Mount Docker binary (if needed)
      - /usr/bin/docker:/usr/bin/docker:ro
    environment:
      - DOCKER_HOST=unix:///var/run/docker.sock
      # Set Jenkins options
      - JENKINS_OPTS=--httpPort=8080
      - JAVA_OPTS=-Djenkins.install.runSetupWizard=false
    networks:
      - jenkins-network
    # Ensure the container has access to Docker group
    group_add:
      - "999"  # Docker group ID (adjust if different on your system)

volumes:
  jenkins_home:
    driver: local

networks:
  jenkins-network:
    driver: bridge