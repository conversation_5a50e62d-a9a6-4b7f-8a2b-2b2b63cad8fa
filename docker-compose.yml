version: '3.8'

services:
  jenkins:
    build: .
    container_name: jenkins-docker
    restart: unless-stopped
    ports:
      - "8080:8080"
      - "50000:50000"
    volumes:
      # Jenkins data persistence
      - jenkins_home:/var/jenkins_home
      # Mount Docker socket from host
      - /var/run/docker.sock:/var/run/docker.sock
      # Mount Docker binaries from host (Docker Desktop on macOS)
      - /Applications/Docker.app/Contents/Resources/bin/docker:/usr/local/bin/docker:ro
      - /Applications/Docker.app/Contents/Resources/bin/docker-compose:/usr/local/bin/docker-compose:ro
    environment:
      - DOCKER_HOST=unix:///var/run/docker.sock
    # Run as root to access Docker socket
    user: root

volumes:
  jenkins_home:
    driver: local
