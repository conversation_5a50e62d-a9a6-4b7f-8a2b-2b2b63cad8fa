# Jenkins with Docker Integration

This setup runs <PERSON> in a Docker container with the ability to execute Docker commands on the host system. Instead of installing Dock<PERSON> inside the Jenkins container, we mount the host's Docker socket and binaries, providing a more efficient "Docker-outside-of-Docker" approach.

## Features

- Jenkins LTS with custom plugins (BlueOcean, Docker workflow, etc.)
- Host Docker socket and binary mounting (no Docker installation in container)
- Docker CLI access from within Jenkins jobs using host Docker daemon
- Persistent Jenkins data storage
- Common development tools (Git, Python, Node.js)

## Quick Start

1. **Build and start Jenkins:**
   ```bash
   docker-compose up -d
   ```

2. **Access Jenkins:**
   - Open http://localhost:8080 in your browser
   - Get the initial admin password:
     ```bash
     docker-compose logs jenkins | grep -A 5 "Please use the following password"
     ```
   - Or directly from the container:
     ```bash
     docker exec jenkins-docker cat /var/jenkins_home/secrets/initialAdminPassword
     ```

3. **Complete Jenkins setup:**
   - Install suggested plugins (or skip and install manually later)
   - Create an admin user
   - Configure Jenkins URL (use http://localhost:8080)

## Testing Docker Integration

Create a simple Jenkins pipeline to test Docker functionality:

```groovy
pipeline {
    agent any
    
    stages {
        stage('Test Docker') {
            steps {
                script {
                    // Test Docker connectivity
                    sh 'docker --version'
                    sh 'docker ps'
                    
                    // Run a simple container
                    sh 'docker run --rm hello-world'
                }
            }
        }
    }
}
```

## Stopping Jenkins

```bash
docker-compose down
```

To remove all data (including Jenkins configuration):
```bash
docker-compose down -v
```

## How it Works

- **Docker Socket Mounting**: `/var/run/docker.sock` from host is mounted to container
- **Docker Binary Mounting**: Host Docker binaries are mounted read-only into the container  
- **No Installation**: No Docker installation happens inside the Jenkins container
- **Host Resources**: Jenkins uses the host's Docker daemon and resources directly

## Architecture

```
Host System (macOS)
├── Docker Desktop
├── Docker Socket (/var/run/docker.sock)
└── Jenkins Container
    ├── Mounts Docker Socket
    ├── Mounts Docker Binaries
    └── Executes Docker commands on host
```

## Security Notes

- This setup runs Jenkins as root with access to the Docker socket
- Only use this for development/testing environments
- For production, consider using dedicated Jenkins agents or rootless Docker

## Troubleshooting

- If you get permission errors, ensure Docker is running on your host
- On macOS, make sure Docker Desktop is running and the socket is available
- Check container logs: `docker-compose logs jenkins`
